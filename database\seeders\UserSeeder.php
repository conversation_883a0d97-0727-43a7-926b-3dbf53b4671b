<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Admin Account
        \App\Models\User::create([
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => bcrypt('admin123'),
            'email_verified_at' => now(),
            'role' => 'admin',
        ]);

        // Pengusaha Rental Account
        \App\Models\User::create([
            'name' => '<PERSON><PERSON> (Rental Motor Jakarta)',
            'email' => '<EMAIL>',
            'password' => bcrypt('rental123'),
            'email_verified_at' => now(),
            'role' => 'pengusaha_rental',
        ]);

        // User Biasa Account
        \App\Models\User::create([
            'name' => 'Siti Nurhaliza',
            'email' => '<EMAIL>',
            'password' => bcrypt('user123'),
            'email_verified_at' => now(),
            'role' => 'user',
        ]);

        // Demo Accounts untuk testing
        \App\Models\User::create([
            'name' => 'Demo Rental Bandung',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
            'role' => 'pengusaha_rental',
        ]);

        \App\Models\User::create([
            'name' => 'Demo Rental Surabaya',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
            'role' => 'pengusaha_rental',
        ]);
    }
}
