{"id": "12345678-1234-1234-1234-123456789012", "name": "<PERSON><PERSON>list - Local", "values": [{"key": "base_url", "value": "http://localhost:8000/api", "type": "default", "enabled": true}, {"key": "token", "value": "Bearer ", "type": "secret", "enabled": true}, {"key": "web_url", "value": "http://localhost:8000", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-01-21T10:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}