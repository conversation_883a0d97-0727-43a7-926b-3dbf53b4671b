# Laravel
/vendor/
/node_modules/
/public/hot
/public/storage
/storage/*.key
/storage/app/public/*
!/storage/app/public/.gitkeep
.env
.env.backup
.env.production
.phpunit.result.cache
Homestead.json
Homestead.yaml
auth.json
npm-debug.log
yarn-error.log

# IDE
/.idea
/.vscode
/.fleet
/.nova
/.zed
.phpactor.json
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
laravel.log

# Testing
/coverage
/.phpunit.cache

# Build
/public/build
/public/mix-manifest.json

# Temporary files
*.tmp
*.temp

# Database
*.sqlite
*.sqlite-journal

# Cache
/bootstrap/cache/*.php
/storage/framework/cache/*
/storage/framework/sessions/*
/storage/framework/testing/*
/storage/framework/views/*
/storage/logs/*
/storage/pail

# Compiled assets
/public/css
/public/js

# Backup files
*.bak
*.backup

# Local development
.vagrant/
docker-compose.override.yml

# Debugbar
/storage/debugbar/
